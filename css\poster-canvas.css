/* <PERSON><PERSON> Poster Maker - Canvas Styles */
/* 海報畫布樣式 */

.kms-canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    background: #f8f9fa;
    position: relative;
    overflow: auto;
}

.kms-poster-canvas {
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Paper Sizes */
.kms-canvas-letter {
    width: 816px;  /* 8.5 inches at 96 DPI */
    height: 1056px; /* 11 inches at 96 DPI */
}

.kms-canvas-4x6 {
    width: 384px;  /* 4 inches at 96 DPI */
    height: 576px;  /* 6 inches at 96 DPI */
}

/* Canvas Elements */
.kms-canvas-element {
    position: absolute;
    cursor: move;
    user-select: none;
    transition: transform 0.1s ease;
}

.kms-canvas-element:hover {
    z-index: 10;
}

.kms-canvas-element.selected {
    z-index: 20;
}

.kms-canvas-element.selected::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px dashed #667eea;
    border-radius: 4px;
    pointer-events: none;
}

/* Text Elements */
.kms-text-element {
    padding: 8px;
    min-width: 50px;
    min-height: 30px;
    word-wrap: break-word;
    white-space: pre-wrap;
    outline: none;
    resize: none;
    border: none;
    background: transparent;
    font-family: inherit;
    line-height: 1.4;
}

.kms-text-element:focus {
    background: rgba(102, 126, 234, 0.05);
}

/* Image Elements */
.kms-image-element {
    max-width: 100%;
    height: auto;
    border-radius: 0;
    object-fit: contain;
}

.kms-image-element.resizable {
    resize: both;
    overflow: hidden;
}

/* QR Code Elements */
.kms-qr-element {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.kms-qr-element img {
    display: block;
    width: 100%;
    height: auto;
}

/* Canvas Borders */
.kms-canvas-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    border-radius: 8px;
}

/* Premium Certificate Border - Multi-layered Gold Frame */
.kms-border-certificate {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d,
        inset 0 0 0 15px #f4e4bc,
        inset 0 0 0 17px #c8860d;
}

.kms-border-certificate::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        );
    z-index: -1;
}

.kms-border-certificate::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid #c8860d;
    background:
        linear-gradient(45deg,
            transparent 0px, transparent 15px,
            #c8860d 15px, #c8860d 17px,
            transparent 17px, transparent 32px
        ),
        linear-gradient(-45deg,
            transparent 0px, transparent 15px,
            #c8860d 15px, #c8860d 17px,
            transparent 17px, transparent 32px
        );
    background-size: 32px 32px;
    z-index: 1;
    pointer-events: none;
}

/* Royal Award Border - Triple Gold Lines */
.kms-border-royal {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 4px #f4e4bc,
        inset 0 0 0 6px #c8860d,
        inset 0 0 0 9px #f4e4bc,
        inset 0 0 0 11px #c8860d,
        inset 0 0 0 14px #f4e4bc,
        inset 0 0 0 16px #c8860d;
}

.kms-border-royal::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 3px,
            #f4e4bc 3px, #f4e4bc 6px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 3px,
            #f4e4bc 3px, #f4e4bc 6px
        );
    z-index: -1;
}

.kms-border-royal::after {
    content: '';
    position: absolute;
    top: 12px;
    left: 12px;
    right: 12px;
    bottom: 12px;
    border: 3px solid #c8860d;
    background:
        linear-gradient(45deg,
            transparent 0px, transparent 20px,
            #c8860d 20px, #c8860d 23px,
            transparent 23px, transparent 43px
        ),
        linear-gradient(-45deg,
            transparent 0px, transparent 20px,
            #c8860d 20px, #c8860d 23px,
            transparent 23px, transparent 43px
        );
    background-size: 43px 43px;
    z-index: 1;
    pointer-events: none;
}



/* Ornate Award Border - Multi-line Gold Certificate */
.kms-border-ornate {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d,
        inset 0 0 0 15px #f4e4bc,
        inset 0 0 0 17px #c8860d,
        inset 0 0 0 19px #f4e4bc,
        inset 0 0 0 21px #c8860d;
}

.kms-border-ornate::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 1px,
            #f4e4bc 1px, #f4e4bc 2px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 1px,
            #f4e4bc 1px, #f4e4bc 2px
        );
    z-index: -1;
}

.kms-border-ornate::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 1px solid #c8860d;
    background:
        linear-gradient(45deg,
            transparent 0px, transparent 12px,
            #c8860d 12px, #c8860d 14px,
            transparent 14px, transparent 26px
        ),
        linear-gradient(-45deg,
            transparent 0px, transparent 12px,
            #c8860d 12px, #c8860d 14px,
            transparent 14px, transparent 26px
        );
    background-size: 26px 26px;
    z-index: 1;
    pointer-events: none;
}

/* Diploma Border */
.kms-border-diploma {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d;
}

.kms-border-diploma::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        );
    z-index: -1;
}

.kms-border-diploma::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border: 2px double #c8860d;
    z-index: 1;
    pointer-events: none;
}

/* Vintage Certificate */
.kms-border-vintage-cert {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d;
}

.kms-border-vintage-cert::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(45deg,
            #c8860d 0px, #c8860d 3px,
            #f4e4bc 3px, #f4e4bc 6px
        );
    z-index: -1;
}

.kms-border-vintage-cert::after {
    content: '';
    position: absolute;
    top: 18px;
    left: 18px;
    right: 18px;
    bottom: 18px;
    border: 1px solid #c8860d;
    background:
        repeating-linear-gradient(0deg,
            transparent 0px, transparent 8px,
            #c8860d 8px, #c8860d 10px,
            transparent 10px, transparent 18px
        ),
        repeating-linear-gradient(90deg,
            transparent 0px, transparent 8px,
            #c8860d 8px, #c8860d 10px,
            transparent 10px, transparent 18px
        );
    z-index: 1;
    pointer-events: none;
}

/* Art Deco Border */
.kms-border-artdeco {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d;
}

.kms-border-artdeco::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        );
    z-index: -1;
}

.kms-border-artdeco::after {
    content: '';
    position: absolute;
    top: 14px;
    left: 14px;
    right: 14px;
    bottom: 14px;
    border: 1px solid #c8860d;
    background:
        linear-gradient(45deg,
            #c8860d 25%, transparent 25%),
        linear-gradient(-45deg,
            #c8860d 25%, transparent 25%),
        linear-gradient(45deg,
            transparent 75%, #c8860d 75%),
        linear-gradient(-45deg,
            transparent 75%, #c8860d 75%);
    background-size: 16px 16px;
    z-index: 1;
    pointer-events: none;
}

/* Elegant Frame */
.kms-border-elegant-frame {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d;
}

.kms-border-elegant-frame::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        );
    z-index: -1;
}

.kms-border-elegant-frame::after {
    content: '';
    position: absolute;
    top: 16px;
    left: 16px;
    right: 16px;
    bottom: 16px;
    border: 3px double #c8860d;
    z-index: 1;
    pointer-events: none;
}

/* Classic Certificate */
.kms-border-classic-cert {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d;
}

.kms-border-classic-cert::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(45deg,
            #c8860d 0px, #c8860d 4px,
            #f4e4bc 4px, #f4e4bc 8px
        );
    z-index: -1;
}

.kms-border-classic-cert::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 2px solid #c8860d;
    z-index: 1;
    pointer-events: none;
}

/* Modern Certificate */
.kms-border-modern-cert {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d;
}

.kms-border-modern-cert::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 2px,
            #f4e4bc 2px, #f4e4bc 4px
        );
    z-index: -1;
}

.kms-border-modern-cert::after {
    content: '';
    position: absolute;
    top: 22px;
    left: 22px;
    right: 22px;
    bottom: 22px;
    border: 1px solid #c8860d;
    border-radius: 8px;
    z-index: 1;
    pointer-events: none;
}

/* Premium Award Certificate - Multi-layered Gold Frame */
.kms-border-premium-award {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d,
        inset 0 0 0 15px #f4e4bc,
        inset 0 0 0 17px #c8860d,
        inset 0 0 0 19px #f4e4bc,
        inset 0 0 0 21px #c8860d,
        inset 0 0 0 23px #f4e4bc,
        inset 0 0 0 25px #c8860d;
}

.kms-border-premium-award::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        repeating-linear-gradient(0deg,
            #c8860d 0px, #c8860d 1px,
            #f4e4bc 1px, #f4e4bc 2px
        ),
        repeating-linear-gradient(90deg,
            #c8860d 0px, #c8860d 1px,
            #f4e4bc 1px, #f4e4bc 2px
        );
    z-index: -1;
}

.kms-border-premium-award::after {
    content: '';
    position: absolute;
    top: 6px;
    left: 6px;
    right: 6px;
    bottom: 6px;
    border: 1px solid #c8860d;
    background:
        linear-gradient(45deg,
            transparent 0px, transparent 10px,
            #c8860d 10px, #c8860d 12px,
            transparent 12px, transparent 22px
        ),
        linear-gradient(-45deg,
            transparent 0px, transparent 10px,
            #c8860d 10px, #c8860d 12px,
            transparent 12px, transparent 22px
        );
    background-size: 22px 22px;
    z-index: 1;
    pointer-events: none;
}

/* Classic Multi-line Certificate Border - Exact Match */
.kms-border-classic-multilayer {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow: 
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d,
        inset 0 0 0 15px #f4e4bc,
        inset 0 0 0 17px #c8860d;
}

.kms-border-classic-multilayer::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background: 
        repeating-linear-gradient(0deg, 
            #c8860d 0px, #c8860d 2px, 
            #f4e4bc 2px, #f4e4bc 4px
        ),
        repeating-linear-gradient(90deg, 
            #c8860d 0px, #c8860d 2px, 
            #f4e4bc 2px, #f4e4bc 4px
        );
    z-index: -1;
}

.kms-border-classic-multilayer::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid #c8860d;
    background: 
        linear-gradient(45deg, 
            transparent 0px, transparent 15px,
            #c8860d 15px, #c8860d 17px,
            transparent 17px, transparent 32px
        ),
        linear-gradient(-45deg, 
            transparent 0px, transparent 15px,
            #c8860d 15px, #c8860d 17px,
            transparent 17px, transparent 32px
        );
    background-size: 32px 32px;
    z-index: 1;
    pointer-events: none;
}

/* Canvas Tools */
.kms-canvas-tools {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    z-index: 100;
}

.kms-canvas-tool {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.kms-canvas-tool:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.kms-canvas-tool.active {
    background: #667eea;
    color: white;
}

/* Resize Handles */
.kms-resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #667eea;
    border: 1px solid white;
    border-radius: 50%;
    cursor: nw-resize;
}

.kms-resize-handle.nw { top: -4px; left: -4px; cursor: nw-resize; }
.kms-resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
.kms-resize-handle.sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.kms-resize-handle.se { bottom: -4px; right: -4px; cursor: se-resize; }
.kms-resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.kms-resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.kms-resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
.kms-resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

/* Print Styles */
@media print {
    .kms-canvas-container {
        padding: 0;
        background: white;
    }
    
    .kms-poster-canvas {
        box-shadow: none;
        border-radius: 0;
        page-break-inside: avoid;
    }
    
    .kms-canvas-element.selected::after {
        display: none;
    }
    
    .kms-canvas-tools,
    .kms-resize-handle {
        display: none !important;
    }
}

/* Animation for canvas transitions */
@keyframes kms-canvas-appear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.kms-poster-canvas {
    animation: kms-canvas-appear 0.3s ease-out;
}

/* Grid overlay for alignment */
.kms-canvas-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    pointer-events: none;
    background-image: 
        linear-gradient(rgba(102, 126, 234, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(102, 126, 234, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    transition: opacity 0.3s ease;
}

.kms-canvas-grid.active {
    opacity: 1;
}
