/* <PERSON><PERSON> Poster Maker - Canvas Styles */
/* 海報畫布樣式 */

.kms-canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    background: #f8f9fa;
    position: relative;
    overflow: auto;
}

.kms-poster-canvas {
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Paper Sizes */
.kms-canvas-letter {
    width: 816px;  /* 8.5 inches at 96 DPI */
    height: 1056px; /* 11 inches at 96 DPI */
}

.kms-canvas-4x6 {
    width: 384px;  /* 4 inches at 96 DPI */
    height: 576px;  /* 6 inches at 96 DPI */
}

/* Canvas Elements */
.kms-canvas-element {
    position: absolute;
    cursor: move;
    user-select: none;
    transition: transform 0.1s ease;
}

.kms-canvas-element:hover {
    z-index: 10;
}

.kms-canvas-element.selected {
    z-index: 20;
}

.kms-canvas-element.selected::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px dashed #667eea;
    border-radius: 4px;
    pointer-events: none;
}

/* Text Elements */
.kms-text-element {
    padding: 8px;
    min-width: 50px;
    min-height: 30px;
    word-wrap: break-word;
    white-space: pre-wrap;
    outline: none;
    resize: none;
    border: none;
    background: transparent;
    font-family: inherit;
    line-height: 1.4;
}

.kms-text-element:focus {
    background: rgba(102, 126, 234, 0.05);
}

/* Image Elements */
.kms-image-element {
    max-width: 100%;
    height: auto;
    border-radius: 0;
    object-fit: contain;
}

.kms-image-element.resizable {
    resize: both;
    overflow: hidden;
}

/* QR Code Elements */
.kms-qr-element {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.kms-qr-element img {
    display: block;
    width: 100%;
    height: auto;
}

/* Canvas Borders */
.kms-canvas-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    border-radius: 8px;
}

/* Premium Certificate Border - Multi-layered Gold Frame */
.kms-border-certificate {
    border: 40px solid transparent;
    background: white;
    position: relative;
    overflow: visible;
}

.kms-border-certificate::before {
    content: '';
    position: absolute;
    top: -40px;
    left: -40px;
    right: -40px;
    bottom: -40px;
    background:
        /* Outer gold frame */
        linear-gradient(45deg, #b8860b 0%, #d4af37 15%, #ffd700 30%, #ffed4e 45%, #ffd700 60%, #d4af37 85%, #b8860b 100%);
    border-radius: 20px;
    z-index: -3;
    box-shadow: 
        0 0 30px rgba(212, 175, 55, 0.6),
        inset 0 0 20px rgba(184, 134, 11, 0.3);
}

.kms-border-certificate::after {
    content: '';
    position: absolute;
    top: -25px;
    left: -25px;
    right: -25px;
    bottom: -25px;
    background:
        /* Inner decorative frame */
        repeating-linear-gradient(
            0deg,
            #8b6914 0px,
            #d4af37 3px,
            #ffd700 6px,
            #d4af37 9px,
            #8b6914 12px
        ),
        repeating-linear-gradient(
            90deg,
            #8b6914 0px,
            #d4af37 3px,
            #ffd700 6px,
            #d4af37 9px,
            #8b6914 12px
        );
    border-radius: 15px;
    z-index: -2;
    border: 3px solid #b8860b;
    box-shadow: 
        inset 0 0 15px rgba(255, 215, 0, 0.4),
        0 0 20px rgba(139, 105, 20, 0.5);
}

/* Additional decorative layer */
.kms-border-certificate {
    box-shadow: 
        inset 0 0 0 8px #ffd700,
        inset 0 0 0 12px #d4af37,
        inset 0 0 0 16px #b8860b,
        inset 0 0 0 20px #8b6914,
        inset 0 0 30px rgba(212, 175, 55, 0.3);
}

/* Royal Award Border - Triple Gold Lines */
.kms-border-royal {
    border: 35px solid transparent;
    background: white;
    position: relative;
    box-shadow: 
        inset 0 0 0 4px #ffd700,
        inset 0 0 0 8px #d4af37,
        inset 0 0 0 12px #b8860b,
        inset 0 0 0 16px #ffd700,
        inset 0 0 0 20px #8b6914,
        inset 0 0 25px rgba(212, 175, 55, 0.4);
}

.kms-border-royal::before {
    content: '';
    position: absolute;
    top: -35px;
    left: -35px;
    right: -35px;
    bottom: -35px;
    background:
        /* Outer ornate frame */
        repeating-conic-gradient(
            from 0deg at 50% 50%, 
            #8b6914 0deg 15deg, 
            #d4af37 15deg 30deg, 
            #ffd700 30deg 45deg, 
            #ffed4e 45deg 60deg,
            #ffd700 60deg 75deg,
            #d4af37 75deg 90deg
        );
    border-radius: 25px;
    z-index: -2;
    border: 5px solid #b8860b;
    box-shadow: 
        0 0 40px rgba(255, 215, 0, 0.7),
        inset 0 0 25px rgba(139, 105, 20, 0.4);
}

.kms-border-royal::after {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background:
        /* Middle decorative layer */
        linear-gradient(45deg, 
            #b8860b 0%, #d4af37 25%, #ffd700 50%, #d4af37 75%, #b8860b 100%
        );
    border-radius: 18px;
    z-index: -1;
    border: 2px solid #8b6914;
    box-shadow:
        inset 0 0 15px rgba(255, 215, 0, 0.5),
        0 0 25px rgba(212, 175, 55, 0.4);
}

/* Ornate Award Border - Multi-line Gold Certificate */
.kms-border-ornate {
    border: 45px solid transparent;
    background: white;
    position: relative;
    box-shadow: 
        inset 0 0 0 2px #ffd700,
        inset 0 0 0 4px #d4af37,
        inset 0 0 0 6px #b8860b,
        inset 0 0 0 8px #ffd700,
        inset 0 0 0 10px #8b6914,
        inset 0 0 0 12px #d4af37,
        inset 0 0 0 14px #ffd700,
        inset 0 0 0 16px #b8860b,
        inset 0 0 30px rgba(212, 175, 55, 0.5);
}

.kms-border-ornate::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background:
        /* Outer decorative frame with corner ornaments */
        radial-gradient(circle at 45px 45px, #ffd700 0%, #d4af37 40%, transparent 60%),
        radial-gradient(circle at calc(100% - 45px) 45px, #ffd700 0%, #d4af37 40%, transparent 60%),
        radial-gradient(circle at 45px calc(100% - 45px), #ffd700 0%, #d4af37 40%, transparent 60%),
        radial-gradient(circle at calc(100% - 45px) calc(100% - 45px), #ffd700 0%, #d4af37 40%, transparent 60%),
        /* Main gradient background */
        linear-gradient(135deg, #8b6914 0%, #b8860b 20%, #d4af37 40%, #ffd700 60%, #d4af37 80%, #8b6914 100%);
    border-radius: 30px;
    z-index: -3;
    border: 6px solid #8b6914;
    box-shadow: 
        0 0 50px rgba(255, 215, 0, 0.8),
        inset 0 0 30px rgba(139, 105, 20, 0.5);
}

.kms-border-ornate::after {
    content: '';
    position: absolute;
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    background:
        /* Middle ornate layer */
        repeating-linear-gradient(
            45deg,
            #b8860b 0px,
            #d4af37 4px,
            #ffd700 8px,
            #ffed4e 12px,
            #ffd700 16px,
            #d4af37 20px,
            #b8860b 24px
        );
    border-radius: 22px;
    z-index: -2;
    border: 4px solid #8b6914;
    box-shadow:
        inset 0 0 20px rgba(255, 215, 0, 0.6),
        0 0 35px rgba(212, 175, 55, 0.5);
}

/* Diploma Border */
.kms-border-diploma {
    border: 15px solid #2c3e50;
    border-image: linear-gradient(45deg, #2c3e50, #34495e, #2c3e50) 15;
}

.kms-border-diploma::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border: 3px double #d4af37;
}

/* Vintage Certificate */
.kms-border-vintage-cert {
    border: 20px solid #8b4513;
    background: linear-gradient(white, white) padding-box,
                repeating-conic-gradient(from 0deg, #8b4513 0deg 45deg, #daa520 45deg 90deg) border-box;
}

/* Art Deco Border */
.kms-border-artdeco {
    border: 25px solid transparent;
    background: white;
    position: relative;
}

.kms-border-artdeco::before {
    content: '';
    position: absolute;
    top: -25px;
    left: -25px;
    right: -25px;
    bottom: -25px;
    background:
        linear-gradient(45deg, #000 25%, transparent 25%),
        linear-gradient(-45deg, #000 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #000 75%),
        linear-gradient(-45deg, transparent 75%, #000 75%),
        linear-gradient(0deg, #d4af37, #ffd700);
    background-size: 20px 20px, 20px 20px, 20px 20px, 20px 20px, 100% 100%;
    z-index: -1;
}

/* Elegant Frame */
.kms-border-elegant-frame {
    border: 18px solid #2c3e50;
    box-shadow:
        inset 0 0 0 3px #d4af37,
        inset 0 0 0 6px #2c3e50,
        0 0 20px rgba(0,0,0,0.3);
}

/* Classic Certificate */
.kms-border-classic-cert {
    border: 22px solid #8b0000;
    border-image:
        repeating-linear-gradient(
            45deg,
            #8b0000 0px,
            #8b0000 10px,
            #ffd700 10px,
            #ffd700 15px,
            #8b0000 15px,
            #8b0000 25px
        ) 22;
}

/* Modern Certificate */
.kms-border-modern-cert {
    border: 16px solid transparent;
    background:
        linear-gradient(white, white) padding-box,
        linear-gradient(135deg, #667eea 0%, #764ba2 25%, #667eea 50%, #764ba2 75%, #667eea 100%) border-box;
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

/* Premium Award Certificate - Multi-layered Gold Frame */
.kms-border-premium-award {
    border: 40px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow: 
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d;
}

.kms-border-premium-award::before {
    content: '';
    position: absolute;
    top: -40px;
    left: -40px;
    right: -40px;
    bottom: -40px;
    background: 
        linear-gradient(0deg, #c8860d 0px, #c8860d 2px, #f4e4bc 2px, #f4e4bc 4px, #c8860d 4px, #c8860d 6px, #f4e4bc 6px, #f4e4bc 8px, #c8860d 8px, #c8860d 10px, #f4e4bc 10px, #f4e4bc 12px, #c8860d 12px, #c8860d 14px, #f4e4bc 14px, #f4e4bc 16px, #c8860d 16px, #c8860d 18px, #f4e4bc 18px, #f4e4bc 20px, #c8860d 20px, #c8860d 22px, #f4e4bc 22px, #f4e4bc 24px, #c8860d 24px, #c8860d 26px, #f4e4bc 26px, #f4e4bc 28px, #c8860d 28px, #c8860d 30px, #f4e4bc 30px, #f4e4bc 32px, #c8860d 32px, #c8860d 34px, #f4e4bc 34px, #f4e4bc 36px, #c8860d 36px, #c8860d 38px, #f4e4bc 38px, #f4e4bc 40px);
    z-index: -1;
}

.kms-border-premium-award::after {
    content: '';
    position: absolute;
    top: -35px;
    left: -35px;
    right: -35px;
    bottom: -35px;
    background: 
        linear-gradient(90deg, #c8860d 0px, #c8860d 2px, #f4e4bc 2px, #f4e4bc 4px, #c8860d 4px, #c8860d 6px, #f4e4bc 6px, #f4e4bc 8px, #c8860d 8px, #c8860d 10px, #f4e4bc 10px, #f4e4bc 12px, #c8860d 12px, #c8860d 14px, #f4e4bc 14px, #f4e4bc 16px, #c8860d 16px, #c8860d 18px, #f4e4bc 18px, #f4e4bc 20px, #c8860d 20px, #c8860d 22px, #f4e4bc 22px, #f4e4bc 24px, #c8860d 24px, #c8860d 26px, #f4e4bc 26px, #f4e4bc 28px, #c8860d 28px, #c8860d 30px, #f4e4bc 30px, #f4e4bc 32px, #c8860d 32px, #c8860d 34px, #f4e4bc 34px, #f4e4bc 35px);
    z-index: -2;
}

/* Classic Multi-line Certificate Border - Exact Match */
.kms-border-classic-multilayer {
    border: 45px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow: 
        inset 0 0 0 1px #c8860d,
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 7px #f4e4bc,
        inset 0 0 0 9px #c8860d,
        inset 0 0 0 11px #f4e4bc,
        inset 0 0 0 13px #c8860d,
        inset 0 0 0 15px #f4e4bc,
        inset 0 0 0 17px #c8860d;
}

.kms-border-classic-multilayer::before {
    content: '';
    position: absolute;
    top: -45px;
    left: -45px;
    right: -45px;
    bottom: -45px;
    background: 
        repeating-linear-gradient(0deg, 
            #c8860d 0px, #c8860d 2px, 
            #f4e4bc 2px, #f4e4bc 4px
        ),
        repeating-linear-gradient(90deg, 
            #c8860d 0px, #c8860d 2px, 
            #f4e4bc 2px, #f4e4bc 4px
        );
    z-index: -1;
}

.kms-border-classic-multilayer::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid #c8860d;
    background: 
        linear-gradient(45deg, 
            transparent 0px, transparent 15px,
            #c8860d 15px, #c8860d 17px,
            transparent 17px, transparent 32px
        ),
        linear-gradient(-45deg, 
            transparent 0px, transparent 15px,
            #c8860d 15px, #c8860d 17px,
            transparent 17px, transparent 32px
        );
    background-size: 32px 32px;
    z-index: 1;
    pointer-events: none;
}

/* Canvas Tools */
.kms-canvas-tools {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    z-index: 100;
}

.kms-canvas-tool {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.kms-canvas-tool:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.kms-canvas-tool.active {
    background: #667eea;
    color: white;
}

/* Resize Handles */
.kms-resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #667eea;
    border: 1px solid white;
    border-radius: 50%;
    cursor: nw-resize;
}

.kms-resize-handle.nw { top: -4px; left: -4px; cursor: nw-resize; }
.kms-resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
.kms-resize-handle.sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.kms-resize-handle.se { bottom: -4px; right: -4px; cursor: se-resize; }
.kms-resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.kms-resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.kms-resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
.kms-resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

/* Print Styles */
@media print {
    .kms-canvas-container {
        padding: 0;
        background: white;
    }
    
    .kms-poster-canvas {
        box-shadow: none;
        border-radius: 0;
        page-break-inside: avoid;
    }
    
    .kms-canvas-element.selected::after {
        display: none;
    }
    
    .kms-canvas-tools,
    .kms-resize-handle {
        display: none !important;
    }
}

/* Animation for canvas transitions */
@keyframes kms-canvas-appear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.kms-poster-canvas {
    animation: kms-canvas-appear 0.3s ease-out;
}

/* Grid overlay for alignment */
.kms-canvas-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    pointer-events: none;
    background-image: 
        linear-gradient(rgba(102, 126, 234, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(102, 126, 234, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    transition: opacity 0.3s ease;
}

.kms-canvas-grid.active {
    opacity: 1;
}
