/* <PERSON><PERSON> Poster Maker - Controls Panel Styles */
/* 控制面板樣式 */

.kms-controls-panel {
    padding: 1.5rem;
    background: white;
    height: 100%;
    overflow-y: auto;
}

.kms-controls-section {
    margin-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
}

.kms-controls-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.kms-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: #667eea;
    border-radius: 2px;
}

/* Paper Size Controls */
.kms-paper-size-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.kms-paper-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.kms-paper-option:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-paper-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.kms-paper-option .size-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.kms-paper-option .size-dimensions {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Text Controls */
.kms-text-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-font-selector {
    position: relative;
}

.kms-font-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    font-size: 1.1rem;
    text-align: center;
}

.kms-text-style-group {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.5rem;
}

.kms-style-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.kms-style-btn:hover {
    background: #e9ecef;
}

.kms-style-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.kms-color-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.kms-color-preview {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    position: relative;
}

.kms-color-preview::after {
    content: attr(data-color);
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    color: #6c757d;
    white-space: nowrap;
}

/* Border Controls */
.kms-border-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-border-style-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.kms-border-style-option {
    aspect-ratio: 1;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.kms-border-style-option:hover {
    border-color: #667eea;
}

.kms-border-style-option.active {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-border-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: #6c757d;
}

/* Image Controls */
.kms-image-upload {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 2rem 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.kms-image-upload:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-image-upload.dragover {
    border-color: #667eea;
    background: #f0f4ff;
    transform: scale(1.02);
}

.kms-upload-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.kms-upload-text {
    color: #6c757d;
    font-size: 0.9rem;
}

.kms-upload-text strong {
    color: #495057;
}

/* QR Code Controls */
.kms-qr-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-qr-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kms-qr-preview img {
    max-width: 100px;
    max-height: 100px;
}

.kms-qr-placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Background Controls */
.kms-background-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-background-options {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.kms-bg-option {
    aspect-ratio: 1;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-bg-option:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.kms-bg-option.active {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Canvas Border Controls */
.kms-canvas-border-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.kms-canvas-border-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kms-canvas-border-option:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-canvas-border-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.kms-border-name {
    font-size: 0.8rem;
    font-weight: 500;
}

/* Action Buttons */
.kms-action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #e9ecef;
}

.kms-print-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.kms-print-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
}

/* Additional form controls */
.kms-border-controls-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.kms-form-label-small {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #495057;
    font-size: 0.8rem;
}

.kms-font-size-display {
    text-align: center;
    margin-top: 0.5rem;
}

.kms-full-width-btn {
    width: 100%;
}

.kms-hidden-section {
    display: none !important;
}

/* QR Code Controls - Same as KMS_Logo_Photo.app */
.kms-qr-url-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 1rem;
    background: #f8f9fa;
}

.kms-qr-url-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

.kms-qr-controls-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.kms-qr-control-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0;
}

.kms-qr-label {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
    min-width: 60px;
}

.kms-qr-size-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-qr-size-input {
    width: 80px;
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    background: white;
}

.kms-qr-size-input:focus {
    outline: none;
    border-color: #667eea;
}

.kms-qr-unit {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.kms-qr-color-picker {
    width: 120px;
    height: 40px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    background: none;
    padding: 0;
}

.kms-qr-color-picker:focus {
    outline: none;
    border-color: #667eea;
}

.kms-qr-generate-btn {
    width: 100%;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.kms-qr-generate-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.kms-qr-generate-btn:active {
    transform: translateY(0);
}

.kms-hidden-file-input {
    display: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kms-controls-panel {
        padding: 1rem;
    }

    .kms-paper-size-options,
    .kms-canvas-border-options {
        grid-template-columns: 1fr;
    }

    .kms-background-options {
        grid-template-columns: repeat(3, 1fr);
    }

    .kms-qr-size-input {
        width: 60px;
    }

    .kms-qr-color-picker {
        width: 100px;
    }
}
