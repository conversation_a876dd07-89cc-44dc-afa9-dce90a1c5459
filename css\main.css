/* <PERSON><PERSON> Poster Maker - Main Styles */
/* 主要樣式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    overflow-x: hidden;
}

.kms-container {
    display: flex;
    height: 100vh;
    max-width: 1920px;
    margin: 0 auto;
}

/* Header */
.kms-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.kms-logo {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-logo::before {
    content: "🎨";
    font-size: 1.8rem;
}

.kms-language-toggle {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-language-toggle:hover {
    background: rgba(255,255,255,0.3);
}

/* Main Content Area */
.kms-main {
    display: flex;
    margin-top: 70px;
    height: calc(100vh - 70px);
    justify-content: center;
    max-width: 1600px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 70px;
}

/* Sidebar */
.kms-sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
    flex-shrink: 0;
}

/* Canvas Area */
.kms-canvas-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    position: relative;
    min-width: 900px;
    max-width: 1200px;
}

/* Utility Classes */
.kms-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.kms-btn-secondary {
    background: #6c757d;
}

.kms-btn-secondary:hover {
    background: #5a6268;
}

.kms-btn-success {
    background: #28a745;
}

.kms-btn-success:hover {
    background: #218838;
}

.kms-btn-danger {
    background: #dc3545;
}

.kms-btn-danger:hover {
    background: #c82333;
}

.kms-input {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
    width: 100%;
}

.kms-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.kms-select {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    background: white;
    cursor: pointer;
    width: 100%;
}

.kms-select:focus {
    outline: none;
    border-color: #667eea;
}

/* Form Groups */
.kms-form-group {
    margin-bottom: 1.5rem;
}

.kms-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

/* Color Picker */
.kms-color-picker {
    width: 50px;
    height: 40px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    background: none;
    padding: 0;
}

/* Range Slider */
.kms-range {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
}

.kms-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.kms-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .kms-sidebar {
        width: 250px;
    }
    
    .kms-header {
        padding: 1rem;
    }
    
    .kms-logo {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .kms-sidebar {
        position: fixed;
        left: -300px;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 999;
        transition: left 0.3s ease;
    }
    
    .kms-sidebar.active {
        left: 0;
    }
    
    .kms-canvas-area {
        width: 100%;
    }
}
