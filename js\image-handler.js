/**
 * KMS Poster Maker - Image Handler
 * 圖片處理模塊
 */

class KMSImageHandler {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.init();
    }
    
    init() {
        this.setupImageControls();
        this.setupDragAndDrop();
    }
    
    setupImageControls() {
        // Image upload button
        const imageUpload = document.getElementById('imageUpload');
        const imageInput = document.getElementById('imageInput');
        
        if (imageUpload && imageInput) {
            imageUpload.addEventListener('click', () => {
                imageInput.click();
            });
            
            imageInput.addEventListener('change', (e) => {
                this.handleImageUpload(e.target.files[0]);
            });
        }
    }
    
    setupDragAndDrop() {
        const imageUpload = document.getElementById('imageUpload');
        const canvas = document.getElementById('posterCanvas');
        
        if (imageUpload) {
            imageUpload.addEventListener('dragover', (e) => {
                e.preventDefault();
                imageUpload.classList.add('dragover');
            });
            
            imageUpload.addEventListener('dragleave', (e) => {
                e.preventDefault();
                imageUpload.classList.remove('dragover');
            });
            
            imageUpload.addEventListener('drop', (e) => {
                e.preventDefault();
                imageUpload.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0 && this.isValidImageFile(files[0])) {
                    this.handleImageUpload(files[0]);
                }
            });
        }
        
        // Also allow dropping directly on canvas
        if (canvas) {
            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                const files = e.dataTransfer.files;
                if (files.length > 0 && this.isValidImageFile(files[0])) {
                    const rect = canvas.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    this.handleImageUpload(files[0], { x, y });
                }
            });
        }
    }
    
    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return validTypes.includes(file.type);
    }
    
    handleImageUpload(file, position = null) {
        if (!this.isValidImageFile(file)) {
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Please select a valid image file (JPG, PNG, GIF, WebP)' 
                : '請選擇有效的圖片文件 (JPG, PNG, GIF, WebP)');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            this.createImageElement(e.target.result, position);
        };
        reader.readAsDataURL(file);
    }
    
    createImageElement(src, position = null) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return null;
        
        const imageContainer = document.createElement('div');
        imageContainer.className = 'kms-canvas-element kms-image-element';
        imageContainer.dataset.elementId = `image_${++this.posterMaker.elementCounter}`;
        
        const img = document.createElement('img');
        img.src = src;
        img.style.width = '100%';
        img.style.height = '100%';
        img.style.objectFit = 'contain';
        img.style.borderRadius = '0px';
        img.draggable = false;
        
        imageContainer.appendChild(img);
        
        // Set initial position and size
        img.onload = () => {
            const maxWidth = Math.min(300, canvas.offsetWidth * 0.4);
            const maxHeight = Math.min(300, canvas.offsetHeight * 0.4);
            
            let width = img.naturalWidth;
            let height = img.naturalHeight;
            
            // Scale down if too large
            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
            }
            
            imageContainer.style.width = width + 'px';
            imageContainer.style.height = height + 'px';
            
            // Set position
            if (position) {
                imageContainer.style.left = Math.max(0, Math.min(position.x, canvas.offsetWidth - width)) + 'px';
                imageContainer.style.top = Math.max(0, Math.min(position.y, canvas.offsetHeight - height)) + 'px';
            } else {
                imageContainer.style.left = '50px';
                imageContainer.style.top = '50px';
            }
            
            // Add resize handles
            this.addResizeHandles(imageContainer);
        };
        
        // Setup interactions
        this.posterMaker.setupElementInteraction(imageContainer);
        this.setupImageInteractions(imageContainer);
        
        // Add to canvas and elements array
        canvas.appendChild(imageContainer);
        this.posterMaker.elements.push(imageContainer);
        this.posterMaker.selectElement(imageContainer);
        
        return imageContainer;
    }
    
    setupImageInteractions(imageElement) {
        // Double-click to replace image
        imageElement.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => {
                if (e.target.files[0]) {
                    this.replaceImage(imageElement, e.target.files[0]);
                }
            };
            input.click();
        });
        
        // Context menu for image options
        imageElement.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showImageContextMenu(e, imageElement);
        });
    }
    
    replaceImage(imageElement, file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = imageElement.querySelector('img');
            if (img) {
                img.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);
    }
    
    addResizeHandles(element) {
        const handles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'w', 'e'];
        
        handles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `kms-resize-handle ${direction}`;
            handle.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                this.startResize(e, element, direction);
            });
            element.appendChild(handle);
        });
    }
    
    startResize(e, element, direction) {
        e.preventDefault();
        
        const startX = e.clientX;
        const startY = e.clientY;
        const startWidth = parseInt(window.getComputedStyle(element).width);
        const startHeight = parseInt(window.getComputedStyle(element).height);
        const startLeft = parseInt(element.style.left);
        const startTop = parseInt(element.style.top);
        
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        
        const handleResize = (e) => {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newWidth = startWidth;
            let newHeight = startHeight;
            let newLeft = startLeft;
            let newTop = startTop;
            
            // Calculate new dimensions based on direction
            switch (direction) {
                case 'se':
                    newWidth = Math.max(50, startWidth + deltaX);
                    newHeight = Math.max(50, startHeight + deltaY);
                    break;
                case 'sw':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newHeight = Math.max(50, startHeight + deltaY);
                    newLeft = startLeft + deltaX;
                    break;
                case 'ne':
                    newWidth = Math.max(50, startWidth + deltaX);
                    newHeight = Math.max(50, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
                case 'nw':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newHeight = Math.max(50, startHeight - deltaY);
                    newLeft = startLeft + deltaX;
                    newTop = startTop + deltaY;
                    break;
                case 'e':
                    newWidth = Math.max(50, startWidth + deltaX);
                    break;
                case 'w':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newLeft = startLeft + deltaX;
                    break;
                case 's':
                    newHeight = Math.max(50, startHeight + deltaY);
                    break;
                case 'n':
                    newHeight = Math.max(50, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
            }
            
            // Constrain to canvas bounds
            newLeft = Math.max(0, Math.min(newLeft, canvas.offsetWidth - newWidth));
            newTop = Math.max(0, Math.min(newTop, canvas.offsetHeight - newHeight));
            newWidth = Math.min(newWidth, canvas.offsetWidth - newLeft);
            newHeight = Math.min(newHeight, canvas.offsetHeight - newTop);
            
            // Apply new dimensions
            element.style.width = newWidth + 'px';
            element.style.height = newHeight + 'px';
            element.style.left = newLeft + 'px';
            element.style.top = newTop + 'px';
        };
        
        const stopResize = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        };
        
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', stopResize);
    }
    
    showImageContextMenu(e, imageElement) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.kms-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
        
        const menu = document.createElement('div');
        menu.className = 'kms-context-menu';
        menu.style.position = 'fixed';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';
        menu.style.background = 'white';
        menu.style.border = '1px solid #ccc';
        menu.style.borderRadius = '4px';
        menu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '150px';
        
        const options = [
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Replace Image' : '替換圖片',
                action: () => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.onchange = (e) => {
                        if (e.target.files[0]) {
                            this.replaceImage(imageElement, e.target.files[0]);
                        }
                    };
                    input.click();
                }
            },
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Add Border Radius' : '添加圓角',
                action: () => this.showBorderRadiusControl(imageElement)
            },
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Delete Image' : '刪除圖片',
                action: () => {
                    imageElement.remove();
                    this.posterMaker.elements = this.posterMaker.elements.filter(el => el !== imageElement);
                    this.posterMaker.selectedElement = null;
                }
            }
        ];
        
        options.forEach(option => {
            const item = document.createElement('div');
            item.textContent = option.text;
            item.style.padding = '8px 12px';
            item.style.cursor = 'pointer';
            item.style.borderBottom = '1px solid #eee';
            item.addEventListener('click', () => {
                option.action();
                menu.remove();
            });
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f0f0f0';
            });
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'white';
            });
            menu.appendChild(item);
        });
        
        document.body.appendChild(menu);
        
        // Remove menu when clicking elsewhere
        setTimeout(() => {
            document.addEventListener('click', () => {
                menu.remove();
            }, { once: true });
        }, 100);
    }
    
    showBorderRadiusControl(imageElement) {
        const img = imageElement.querySelector('img');
        if (!img) return;
        
        const currentRadius = parseInt(img.style.borderRadius) || 0;
        const newRadius = prompt(
            this.posterMaker.currentLanguage === 'en' 
                ? `Enter border radius (0-50px). Current: ${currentRadius}px`
                : `輸入圓角半徑 (0-50px)。當前: ${currentRadius}px`,
            currentRadius
        );
        
        if (newRadius !== null && !isNaN(newRadius)) {
            const radius = Math.max(0, Math.min(50, parseInt(newRadius)));
            img.style.borderRadius = radius + 'px';
        }
    }
    
    // Update controls when an image element is selected
    updateControlsForImageElement(element) {
        // Show image controls section
        const imageControlsSection = document.getElementById('imageControlsSection');
        if (imageControlsSection) {
            imageControlsSection.style.display = 'block';
            imageControlsSection.classList.remove('kms-hidden-section');
        }

        // Hide other control sections
        const textControlsSection = document.getElementById('textControlsSection');
        const qrControlsSection = document.getElementById('qrControlsSection');
        if (textControlsSection) {
            textControlsSection.style.display = 'none';
            textControlsSection.classList.add('kms-hidden-section');
        }
        if (qrControlsSection) {
            qrControlsSection.style.display = 'none';
            qrControlsSection.classList.add('kms-hidden-section');
        }
    }
}

// Initialize image handler when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.kmsPosterMaker) {
        window.kmsPosterMaker.imageHandler = new KMSImageHandler(window.kmsPosterMaker);
    }
});
