/**
 * KMS Poster Maker - Main Application Controller
 * 主應用程序控制器
 */

class KMSPosterMaker {
    constructor() {
        this.currentLanguage = 'en';
        this.currentPaperSize = 'letter';
        this.selectedElement = null;
        this.elements = [];
        this.elementCounter = 0;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        
        this.translations = {
            en: {
                title: 'KMS Poster Maker',
                paperSize: 'Paper Size',
                letter: 'Letter (8.5" × 11")',
                size4x6: '4" × 6"',
                addElements: 'Add Elements',
                addText: 'Add Text',
                addImage: 'Add Image',
                addQR: 'Add QR Code',
                background: 'Background',
                borders: 'Canvas Borders',
                print: 'Print Poster',
                textControls: 'Text Controls',
                imageControls: 'Image Controls',
                qrControls: 'QR Code Controls',
                font: 'Font',
                fontSize: 'Font Size',
                fontColor: 'Font Color',
                textBorder: 'Text Border',
                borderWidth: 'Border Width',
                borderColor: 'Border Color',
                borderRadius: 'Border Radius',
                enterUrl: 'Enter URL for QR Code',
                generate: 'Generate QR Code',
                uploadImage: 'Click to upload or drag image here',
                supportedFormats: 'Supported: JPG, PNG, GIF'
            },
            zh: {
                title: 'KMS 海報製作器',
                paperSize: '紙張尺寸',
                letter: 'Letter (8.5" × 11")',
                size4x6: '4" × 6"',
                addElements: '添加元素',
                addText: '添加文字',
                addImage: '添加圖片',
                addQR: '添加二維碼',
                background: '背景設置',
                borders: '畫布邊框',
                print: '打印海報',
                textControls: '文字控制',
                imageControls: '圖片控制',
                qrControls: '二維碼控制',
                font: '字體',
                fontSize: '字體大小',
                fontColor: '字體顏色',
                textBorder: '文字邊框',
                borderWidth: '邊框寬度',
                borderColor: '邊框顏色',
                borderRadius: '邊框圓角',
                enterUrl: '輸入二維碼網址',
                generate: '生成二維碼',
                uploadImage: '點擊上傳或拖拽圖片到此處',
                supportedFormats: '支持格式：JPG, PNG, GIF'
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateLanguage();
        this.setupCanvas();
        this.loadFonts();
    }
    
    setupEventListeners() {
        // Language toggle
        const langToggle = document.getElementById('languageToggle');
        if (langToggle) {
            langToggle.addEventListener('click', () => this.toggleLanguage());
        }
        
        // Paper size controls
        const paperSizeOptions = document.querySelectorAll('.kms-paper-option');
        paperSizeOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const size = e.currentTarget.dataset.size;
                this.changePaperSize(size);
            });
        });
        
        // Add element buttons
        document.getElementById('addTextBtn')?.addEventListener('click', () => this.addTextElement());
        document.getElementById('addImageBtn')?.addEventListener('click', () => this.triggerImageUpload());
        document.getElementById('addQRBtn')?.addEventListener('click', () => this.showQRGenerator());
        
        // Print button
        document.getElementById('printBtn')?.addEventListener('click', () => this.printPoster());
        
        // Canvas click handler
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        }
        
        // Background color
        document.getElementById('bgColor')?.addEventListener('change', (e) => {
            this.changeBackgroundColor(e.target.value);
        });

        // Canvas border options
        const borderOptions = document.querySelectorAll('.kms-canvas-border-option');
        borderOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const borderType = e.currentTarget.dataset.border;
                this.changeCanvasBorder(borderType);
            });
        });

        // Canvas tools
        document.getElementById('gridToggle')?.addEventListener('click', () => this.toggleGrid());
        document.getElementById('zoomIn')?.addEventListener('click', () => this.zoomCanvas(1.1));
        document.getElementById('zoomOut')?.addEventListener('click', () => this.zoomCanvas(0.9));

        // Paper radius control
        const paperRadius = document.getElementById('paperRadius');
        const paperRadiusValue = document.getElementById('paperRadiusValue');
        if (paperRadius && paperRadiusValue) {
            paperRadius.addEventListener('input', (e) => {
                const radius = e.target.value + 'px';
                paperRadiusValue.textContent = radius;
                this.changePaperRadius(radius);
            });
        }
        
        // Global key handlers
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Prevent default drag behavior on canvas
        const canvasContainer = document.querySelector('.kms-canvas-container');
        if (canvasContainer) {
            canvasContainer.addEventListener('dragover', (e) => e.preventDefault());
            canvasContainer.addEventListener('drop', (e) => e.preventDefault());
        }
    }
    
    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'en' ? 'zh' : 'en';
        this.updateLanguage();
    }
    
    updateLanguage() {
        const t = this.translations[this.currentLanguage];
        
        // Update all translatable elements
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.dataset.translate;
            if (t[key]) {
                if (element.tagName === 'INPUT' && element.type !== 'button') {
                    element.placeholder = t[key];
                } else {
                    element.textContent = t[key];
                }
            }
        });
        
        // Update language toggle button
        const langToggle = document.getElementById('languageToggle');
        if (langToggle) {
            langToggle.textContent = this.currentLanguage === 'en' ? '中文' : 'English';
        }
    }
    
    changePaperSize(size) {
        this.currentPaperSize = size;
        const canvas = document.getElementById('posterCanvas');
        
        // Remove existing size classes
        canvas.classList.remove('kms-canvas-letter', 'kms-canvas-4x6');
        
        // Add new size class
        canvas.classList.add(`kms-canvas-${size}`);
        
        // Update active paper option
        document.querySelectorAll('.kms-paper-option').forEach(option => {
            option.classList.toggle('active', option.dataset.size === size);
        });
        
        // Reposition elements if they're outside new bounds
        this.repositionElementsInBounds();
    }
    
    setupCanvas() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Set initial paper size
        canvas.classList.add('kms-canvas-letter');
        
        // Mark first paper option as active
        const firstOption = document.querySelector('.kms-paper-option[data-size="letter"]');
        if (firstOption) {
            firstOption.classList.add('active');
        }
    }
    
    loadFonts() {
        // Load Google Fonts for additional options (optional)
        const fonts = [
            'Roboto:300,400,500,700',
            'Open+Sans:300,400,600,700'
        ];

        const link = document.createElement('link');
        link.href = `https://fonts.googleapis.com/css2?${fonts.map(f => `family=${f}`).join('&')}&display=swap`;
        link.rel = 'stylesheet';
        document.head.appendChild(link);
    }
    
    addTextElement() {
        // Use text handler if available
        if (this.textHandler) {
            const textElement = this.textHandler.createTextElement();
            if (textElement) {
                this.selectElement(textElement);
            }
            return;
        }

        // Fallback implementation
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        const textElement = document.createElement('div');
        textElement.className = 'kms-canvas-element kms-text-element';
        textElement.contentEditable = true;
        textElement.textContent = this.currentLanguage === 'en' ? 'Double click to edit' : '雙擊編輯文字';
        textElement.style.left = '50px';
        textElement.style.top = '50px';
        textElement.style.fontSize = '24px';
        textElement.style.fontFamily = 'Roboto, sans-serif';
        textElement.style.color = '#333333';
        textElement.dataset.elementId = `text_${++this.elementCounter}`;

        this.setupElementInteraction(textElement);
        canvas.appendChild(textElement);
        this.elements.push(textElement);
        this.selectElement(textElement);
    }
    
    setupElementInteraction(element) {
        // Mouse down for dragging
        element.addEventListener('mousedown', (e) => this.startDrag(e, element));
        
        // Double click for editing (text elements)
        if (element.classList.contains('kms-text-element')) {
            element.addEventListener('dblclick', (e) => {
                e.stopPropagation();
                element.focus();
            });
        }
        
        // Click to select
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });
    }
    
    startDrag(e, element) {
        // Delegate to drag drop handler if available
        if (this.dragDropHandler) {
            return this.dragDropHandler.startDrag(e, element);
        }

        if (e.target.contentEditable === 'true' && e.detail === 2) return; // Don't drag on double-click edit

        e.preventDefault();
        this.isDragging = true;
        this.selectedElement = element;

        const rect = element.getBoundingClientRect();
        const canvasRect = document.getElementById('posterCanvas').getBoundingClientRect();

        // Calculate offset relative to canvas, not viewport
        this.dragOffset = {
            x: e.clientX - canvasRect.left - parseInt(element.style.left || 0),
            y: e.clientY - canvasRect.top - parseInt(element.style.top || 0)
        };

        document.addEventListener('mousemove', this.handleDrag.bind(this));
        document.addEventListener('mouseup', this.stopDrag.bind(this));

        element.style.cursor = 'grabbing';
    }
    
    handleDrag(e) {
        if (!this.isDragging || !this.selectedElement) return;

        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();

        // Calculate new position relative to canvas
        let newX = e.clientX - canvasRect.left - this.dragOffset.x;
        let newY = e.clientY - canvasRect.top - this.dragOffset.y;

        // Get element dimensions from style or computed values
        const elementWidth = parseInt(this.selectedElement.style.width) || this.selectedElement.offsetWidth;
        const elementHeight = parseInt(this.selectedElement.style.height) || this.selectedElement.offsetHeight;

        // Constrain to canvas bounds
        newX = Math.max(0, Math.min(newX, canvas.offsetWidth - elementWidth));
        newY = Math.max(0, Math.min(newY, canvas.offsetHeight - elementHeight));

        this.selectedElement.style.left = newX + 'px';
        this.selectedElement.style.top = newY + 'px';
    }
    
    stopDrag() {
        this.isDragging = false;
        if (this.selectedElement) {
            this.selectedElement.style.cursor = 'move';
        }
        document.removeEventListener('mousemove', this.handleDrag);
        document.removeEventListener('mouseup', this.stopDrag);
    }
    
    selectElement(element) {
        // Remove selection from all elements
        document.querySelectorAll('.kms-canvas-element').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Select the clicked element
        element.classList.add('selected');
        this.selectedElement = element;
        
        // Update controls based on element type
        this.updateControlsForElement(element);
    }
    
    updateControlsForElement(element) {
        // Hide all control sections first
        const textControlsSection = document.getElementById('textControlsSection');
        const imageControlsSection = document.getElementById('imageControlsSection');
        const qrControlsSection = document.getElementById('qrControlsSection');

        if (textControlsSection) textControlsSection.style.display = 'none';
        if (imageControlsSection) imageControlsSection.style.display = 'none';
        if (qrControlsSection) qrControlsSection.style.display = 'none';

        // Show relevant controls based on element type
        if (element.classList.contains('kms-text-element')) {
            if (textControlsSection) {
                textControlsSection.style.display = 'block';
                textControlsSection.classList.remove('kms-hidden-section');
            }
            if (this.textHandler) {
                this.textHandler.updateControlsForTextElement(element);
            }
        } else if (element.classList.contains('kms-image-element')) {
            if (imageControlsSection) {
                imageControlsSection.style.display = 'block';
                imageControlsSection.classList.remove('kms-hidden-section');
            }
            if (this.imageHandler) {
                this.imageHandler.updateControlsForImageElement(element);
            }
        } else if (element.classList.contains('kms-qr-element')) {
            if (qrControlsSection) {
                qrControlsSection.style.display = 'block';
                qrControlsSection.classList.remove('kms-hidden-section');
            }
            if (this.qrGenerator) {
                this.qrGenerator.updateControlsForQRElement(element);
            }
        }
    }
    
    handleCanvasClick(e) {
        if (e.target.id === 'posterCanvas') {
            // Clicked on empty canvas - deselect all
            this.selectedElement = null;
            document.querySelectorAll('.kms-canvas-element').forEach(el => {
                el.classList.remove('selected');
            });
        }
    }
    
    handleKeyDown(e) {
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteSelectedElement();
        }
    }
    
    deleteSelectedElement() {
        if (this.selectedElement) {
            this.selectedElement.remove();
            this.elements = this.elements.filter(el => el !== this.selectedElement);
            this.selectedElement = null;
        }
    }
    
    repositionElementsInBounds() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        this.elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            let left = parseInt(element.style.left) || 0;
            let top = parseInt(element.style.top) || 0;
            
            // Adjust if outside bounds
            if (left + rect.width > canvas.offsetWidth) {
                left = canvas.offsetWidth - rect.width;
            }
            if (top + rect.height > canvas.offsetHeight) {
                top = canvas.offsetHeight - rect.height;
            }
            
            element.style.left = Math.max(0, left) + 'px';
            element.style.top = Math.max(0, top) + 'px';
        });
    }
    
    changeBackgroundColor(color) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.style.backgroundColor = color;
        }
    }
    
    triggerImageUpload() {
        if (this.imageHandler) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => {
                if (e.target.files[0]) {
                    this.imageHandler.handleImageUpload(e.target.files[0]);
                }
            };
            input.click();
        }
    }

    showQRGenerator() {
        if (this.qrGenerator) {
            const qrControlsSection = document.getElementById('qrControlsSection');
            if (qrControlsSection) {
                qrControlsSection.style.display = 'block';
                qrControlsSection.classList.remove('kms-hidden-section');
                const urlInput = document.getElementById('qrUrl');
                if (urlInput) {
                    urlInput.focus();
                }
            }
        }
    }
    
    printPoster() {
        if (this.printHandler) {
            this.printHandler.printPoster();
        } else {
            window.print();
        }
    }

    changeCanvasBorder(borderType) {
        const canvas = document.getElementById('posterCanvas');
        const borderElement = document.getElementById('canvasBorder');

        if (!canvas || !borderElement) return;

        // Remove existing border classes
        borderElement.className = 'kms-canvas-border';

        // Add new border class if not 'none'
        if (borderType !== 'none') {
            borderElement.classList.add(`kms-border-${borderType}`);
        }

        // Update active border option
        document.querySelectorAll('.kms-canvas-border-option').forEach(option => {
            option.classList.toggle('active', option.dataset.border === borderType);
        });
    }

    toggleGrid() {
        const grid = document.getElementById('canvasGrid');
        const gridToggle = document.getElementById('gridToggle');

        if (grid && gridToggle) {
            grid.classList.toggle('active');
            gridToggle.classList.toggle('active');
        }
    }

    zoomCanvas(factor) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        const currentTransform = canvas.style.transform || 'scale(1)';
        const currentScale = parseFloat(currentTransform.match(/scale\(([^)]+)\)/)?.[1] || 1);
        const newScale = Math.max(0.5, Math.min(2, currentScale * factor));

        canvas.style.transform = `scale(${newScale})`;
        canvas.style.transformOrigin = 'center center';
    }

    changePaperRadius(radius) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.style.borderRadius = radius;
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.kmsPosterMaker = new KMSPosterMaker();
});
