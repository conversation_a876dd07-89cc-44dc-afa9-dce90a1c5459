/**
 * <PERSON><PERSON> Poster Maker - Print Handler
 * 打印處理模塊
 */

class KMSPrintHandler {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.init();
    }
    
    init() {
        this.setupPrintControls();
        this.createPrintStyles();
    }
    
    setupPrintControls() {
        const printBtn = document.getElementById('printBtn');
        if (printBtn) {
            printBtn.addEventListener('click', () => {
                this.printPoster();
            });
        }
        
        // Add keyboard shortcut Ctrl+P
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.printPoster();
            }
        });
    }
    
    createPrintStyles() {
        const printStyles = document.createElement('style');
        printStyles.id = 'kms-print-styles';
        printStyles.textContent = `
            @media print {
                @page {
                    margin: 0;
                    size: auto;
                }
                
                body * {
                    visibility: hidden;
                }
                
                .kms-poster-canvas,
                .kms-poster-canvas * {
                    visibility: visible;
                }
                
                .kms-poster-canvas {
                    position: absolute !important;
                    left: 0 !important;
                    top: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    box-shadow: none !important;
                    border-radius: 0 !important;
                    transform: none !important;
                    page-break-inside: avoid;
                }
                
                .kms-canvas-element.selected::after,
                .kms-resize-handle,
                .kms-canvas-tools,
                .kms-alignment-guide,
                .kms-position-indicator {
                    display: none !important;
                }
                
                .kms-canvas-grid {
                    display: none !important;
                }
                
                /* Letter size specific */
                .kms-canvas-letter {
                    width: 8.5in !important;
                    height: 11in !important;
                }
                
                /* 4x6 size specific */
                .kms-canvas-4x6 {
                    width: 4in !important;
                    height: 6in !important;
                }
            }
        `;
        document.head.appendChild(printStyles);
    }
    
    printPoster() {
        // Show print preparation dialog
        this.showPrintDialog();
    }
    
    showPrintDialog() {
        // Create print dialog overlay
        const overlay = document.createElement('div');
        overlay.className = 'kms-print-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        const dialog = document.createElement('div');
        dialog.className = 'kms-print-dialog';
        dialog.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        `;
        
        const title = document.createElement('h2');
        title.textContent = this.posterMaker.currentLanguage === 'en' ? 'Print Settings' : '打印設置';
        title.style.cssText = `
            margin: 0 0 1.5rem 0;
            color: #333;
            font-size: 1.5rem;
        `;
        
        // Print options
        const optionsContainer = document.createElement('div');
        optionsContainer.style.marginBottom = '2rem';
        
        // Quality option
        const qualityGroup = this.createOptionGroup(
            this.posterMaker.currentLanguage === 'en' ? 'Print Quality' : '打印質量',
            [
                { value: 'draft', label: this.posterMaker.currentLanguage === 'en' ? 'Draft (Fast)' : '草稿 (快速)' },
                { value: 'normal', label: this.posterMaker.currentLanguage === 'en' ? 'Normal' : '普通', selected: true },
                { value: 'high', label: this.posterMaker.currentLanguage === 'en' ? 'High Quality' : '高質量' }
            ],
            'radio',
            'printQuality'
        );
        
        // Orientation option
        const orientationGroup = this.createOptionGroup(
            this.posterMaker.currentLanguage === 'en' ? 'Orientation' : '方向',
            [
                { value: 'portrait', label: this.posterMaker.currentLanguage === 'en' ? 'Portrait' : '縱向', selected: this.posterMaker.currentPaperSize === 'letter' },
                { value: 'landscape', label: this.posterMaker.currentLanguage === 'en' ? 'Landscape' : '橫向', selected: this.posterMaker.currentPaperSize === '4x6' }
            ],
            'radio',
            'printOrientation'
        );
        
        // Scale option
        const scaleGroup = this.createOptionGroup(
            this.posterMaker.currentLanguage === 'en' ? 'Scale' : '縮放',
            [
                { value: 'fit', label: this.posterMaker.currentLanguage === 'en' ? 'Fit to Page' : '適合頁面', selected: true },
                { value: 'actual', label: this.posterMaker.currentLanguage === 'en' ? 'Actual Size' : '實際大小' },
                { value: 'custom', label: this.posterMaker.currentLanguage === 'en' ? 'Custom' : '自定義' }
            ],
            'radio',
            'printScale'
        );
        
        optionsContainer.appendChild(qualityGroup);
        optionsContainer.appendChild(orientationGroup);
        optionsContainer.appendChild(scaleGroup);
        
        // Buttons
        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.cssText = `
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        `;
        
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Cancel' : '取消';
        cancelBtn.className = 'kms-btn kms-btn-secondary';
        cancelBtn.addEventListener('click', () => {
            overlay.remove();
        });
        
        const previewBtn = document.createElement('button');
        previewBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Preview' : '預覽';
        previewBtn.className = 'kms-btn';
        previewBtn.addEventListener('click', () => {
            this.showPrintPreview();
            overlay.remove();
        });
        
        const printBtn = document.createElement('button');
        printBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Print' : '打印';
        printBtn.className = 'kms-btn kms-btn-success';
        printBtn.addEventListener('click', () => {
            this.executePrint();
            overlay.remove();
        });
        
        buttonsContainer.appendChild(cancelBtn);
        buttonsContainer.appendChild(previewBtn);
        buttonsContainer.appendChild(printBtn);
        
        dialog.appendChild(title);
        dialog.appendChild(optionsContainer);
        dialog.appendChild(buttonsContainer);
        overlay.appendChild(dialog);
        document.body.appendChild(overlay);
        
        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }
    
    createOptionGroup(title, options, type, name) {
        const group = document.createElement('div');
        group.style.marginBottom = '1.5rem';
        
        const label = document.createElement('label');
        label.textContent = title;
        label.style.cssText = `
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        `;
        
        const optionsContainer = document.createElement('div');
        optionsContainer.style.cssText = `
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        `;
        
        options.forEach(option => {
            const optionContainer = document.createElement('div');
            optionContainer.style.cssText = `
                display: flex;
                align-items: center;
                gap: 0.5rem;
            `;
            
            const input = document.createElement('input');
            input.type = type;
            input.name = name;
            input.value = option.value;
            input.checked = option.selected || false;
            input.id = `${name}_${option.value}`;
            
            const optionLabel = document.createElement('label');
            optionLabel.textContent = option.label;
            optionLabel.htmlFor = input.id;
            optionLabel.style.cursor = 'pointer';
            
            optionContainer.appendChild(input);
            optionContainer.appendChild(optionLabel);
            optionsContainer.appendChild(optionContainer);
        });
        
        group.appendChild(label);
        group.appendChild(optionsContainer);
        return group;
    }
    
    showPrintPreview() {
        // Create preview window
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (!previewWindow) {
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Please allow popups to show print preview'
                : '請允許彈出窗口以顯示打印預覽');
            return;
        }
        
        const canvas = document.getElementById('posterCanvas');
        const canvasHTML = canvas.outerHTML;
        
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${this.posterMaker.currentLanguage === 'en' ? 'Print Preview' : '打印預覽'}</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: Arial, sans-serif;
                        background: #f5f5f5;
                    }
                    .preview-container {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: calc(100vh - 40px);
                    }
                    .kms-poster-canvas {
                        background: white;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        position: relative;
                        overflow: hidden;
                    }
                    .kms-canvas-letter {
                        width: 816px;
                        height: 1056px;
                    }
                    .kms-canvas-4x6 {
                        width: 384px;
                        height: 576px;
                    }
                    .kms-canvas-element {
                        position: absolute;
                    }
                    .kms-text-element {
                        padding: 8px;
                        word-wrap: break-word;
                        white-space: pre-wrap;
                        line-height: 1.4;
                    }
                    .kms-image-element img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                    .kms-qr-element img {
                        display: block;
                        width: 100%;
                        height: auto;
                    }
                    .kms-resize-handle,
                    .kms-canvas-element.selected::after {
                        display: none !important;
                    }
                    .print-controls {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        display: flex;
                        gap: 10px;
                    }
                    .btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 14px;
                    }
                    .btn-primary {
                        background: #667eea;
                        color: white;
                    }
                    .btn-secondary {
                        background: #6c757d;
                        color: white;
                    }
                </style>
            </head>
            <body>
                <div class="print-controls">
                    <button class="btn btn-primary" onclick="window.print()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Print' : '打印'}
                    </button>
                    <button class="btn btn-secondary" onclick="window.close()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Close' : '關閉'}
                    </button>
                </div>
                <div class="preview-container">
                    ${canvasHTML}
                </div>
            </body>
            </html>
        `);
        
        previewWindow.document.close();
    }
    
    executePrint() {
        // Prepare for printing
        this.preparePrintLayout();
        
        // Add print-specific styles temporarily
        const printStyleOverride = document.createElement('style');
        printStyleOverride.id = 'kms-print-override';
        printStyleOverride.textContent = `
            @media print {
                .kms-canvas-element.selected {
                    outline: none !important;
                }
                .kms-canvas-element.selected::after {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(printStyleOverride);
        
        // Clear selection before printing
        const selectedElement = this.posterMaker.selectedElement;
        if (selectedElement) {
            selectedElement.classList.remove('selected');
        }
        
        // Execute print
        setTimeout(() => {
            window.print();
            
            // Restore selection after print dialog
            setTimeout(() => {
                if (selectedElement) {
                    selectedElement.classList.add('selected');
                }
                printStyleOverride.remove();
            }, 1000);
        }, 100);
    }
    
    preparePrintLayout() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Store original styles
        const originalStyles = {
            position: canvas.style.position,
            transform: canvas.style.transform,
            boxShadow: canvas.style.boxShadow
        };
        
        // Apply print-ready styles
        canvas.style.position = 'relative';
        canvas.style.transform = 'none';
        canvas.style.boxShadow = 'none';
        
        // Restore after print (when print dialog closes)
        const mediaQueryList = window.matchMedia('print');
        const handlePrintEnd = () => {
            canvas.style.position = originalStyles.position;
            canvas.style.transform = originalStyles.transform;
            canvas.style.boxShadow = originalStyles.boxShadow;
            mediaQueryList.removeListener(handlePrintEnd);
        };
        
        mediaQueryList.addListener(handlePrintEnd);
    }
    
    // Export poster as image
    exportAsImage(format = 'png', quality = 0.9) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Create a new canvas for export
        const exportCanvas = document.createElement('canvas');
        const ctx = exportCanvas.getContext('2d');
        
        // Set canvas size based on paper size
        const scale = 2; // Higher resolution
        if (this.posterMaker.currentPaperSize === 'letter') {
            exportCanvas.width = 816 * scale;
            exportCanvas.height = 1056 * scale;
        } else {
            exportCanvas.width = 384 * scale;
            exportCanvas.height = 576 * scale;
        }
        
        ctx.scale(scale, scale);
        
        // Fill background
        const bgColor = canvas.style.backgroundColor || '#ffffff';
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, exportCanvas.width / scale, exportCanvas.height / scale);
        
        // This would require html2canvas library for full implementation
        // For now, we'll use a simpler approach
        this.downloadCanvasAsImage(exportCanvas, format, quality);
    }
    
    downloadCanvasAsImage(canvas, format, quality) {
        const link = document.createElement('a');
        link.download = `poster_${Date.now()}.${format}`;
        link.href = canvas.toDataURL(`image/${format}`, quality);
        link.click();
    }
    
    // Save poster data as JSON
    savePosterData() {
        const posterData = {
            paperSize: this.posterMaker.currentPaperSize,
            backgroundColor: document.getElementById('posterCanvas').style.backgroundColor,
            elements: this.posterMaker.elements.map(element => ({
                id: element.dataset.elementId,
                type: element.classList.contains('kms-text-element') ? 'text' : 
                      element.classList.contains('kms-image-element') ? 'image' : 'qr',
                position: {
                    x: parseInt(element.style.left) || 0,
                    y: parseInt(element.style.top) || 0
                },
                size: {
                    width: element.offsetWidth,
                    height: element.offsetHeight
                },
                styles: element.style.cssText,
                content: element.textContent || element.dataset.qrUrl || element.querySelector('img')?.src
            })),
            timestamp: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(posterData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `poster_${Date.now()}.json`;
        link.click();
    }
    
    // Load poster data from JSON
    loadPosterData(jsonData) {
        try {
            const posterData = JSON.parse(jsonData);
            
            // Clear current poster
            this.posterMaker.elements.forEach(element => element.remove());
            this.posterMaker.elements = [];
            
            // Set paper size
            this.posterMaker.changePaperSize(posterData.paperSize);
            
            // Set background color
            const canvas = document.getElementById('posterCanvas');
            canvas.style.backgroundColor = posterData.backgroundColor;
            
            // Recreate elements
            posterData.elements.forEach(elementData => {
                // This would need to be implemented based on element type
                console.log('Loading element:', elementData);
            });
            
        } catch (error) {
            console.error('Error loading poster data:', error);
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Error loading poster data'
                : '加載海報數據時出錯');
        }
    }
}

// Initialize print handler when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.kmsPosterMaker) {
        window.kmsPosterMaker.printHandler = new KMSPrintHandler(window.kmsPosterMaker);
    }
});
