/**
 * KMS Poster Maker - Text Handler
 * 文字處理模塊
 */

class KMSTextHandler {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.init();
    }
    
    init() {
        this.setupTextControls();
    }
    
    setupTextControls() {
        // Font family control
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                this.updateSelectedTextStyle('fontFamily', e.target.value);
            });
        }
        
        // Font size control
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        if (fontSize && fontSizeValue) {
            fontSize.addEventListener('input', (e) => {
                const size = e.target.value + 'px';
                fontSizeValue.textContent = size;
                this.updateSelectedTextStyle('fontSize', size);
            });
        }
        
        // Font color control
        const fontColor = document.getElementById('fontColor');
        if (fontColor) {
            fontColor.addEventListener('change', (e) => {
                this.updateSelectedTextStyle('color', e.target.value);
                this.updateColorPreview(fontColor, e.target.value);
            });
        }
        
        // Text style buttons (bold, italic, underline)
        const styleButtons = document.querySelectorAll('.kms-style-btn');
        styleButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const style = btn.dataset.style;
                this.toggleTextStyle(btn, style);
            });
        });
        
        // Border controls
        const borderWidth = document.getElementById('borderWidth');
        if (borderWidth) {
            borderWidth.addEventListener('input', (e) => {
                this.updateSelectedTextBorder('borderWidth', e.target.value + 'px');
            });
        }
        
        const borderRadius = document.getElementById('borderRadius');
        if (borderRadius) {
            borderRadius.addEventListener('input', (e) => {
                this.updateSelectedTextBorder('borderRadius', e.target.value + 'px');
            });
        }
        
        const borderColor = document.getElementById('borderColor');
        if (borderColor) {
            borderColor.addEventListener('change', (e) => {
                this.updateSelectedTextBorder('borderColor', e.target.value);
                this.updateColorPreview(borderColor, e.target.value);
            });
        }

        // Text background color
        const textBgColor = document.getElementById('textBgColor');
        if (textBgColor) {
            textBgColor.addEventListener('change', (e) => {
                this.updateSelectedTextStyle('backgroundColor', e.target.value);
                this.updateColorPreview(textBgColor, e.target.value);
            });
        }

        // Text shadow controls
        const shadowBlur = document.getElementById('shadowBlur');
        if (shadowBlur) {
            shadowBlur.addEventListener('input', (e) => {
                this.updateTextShadow();
            });
        }

        const shadowDistance = document.getElementById('shadowDistance');
        if (shadowDistance) {
            shadowDistance.addEventListener('input', (e) => {
                this.updateTextShadow();
            });
        }

        const shadowColor = document.getElementById('shadowColor');
        if (shadowColor) {
            shadowColor.addEventListener('change', (e) => {
                this.updateTextShadow();
                this.updateColorPreview(shadowColor, e.target.value);
            });
        }

        // Text alignment controls
        const alignButtons = document.querySelectorAll('[data-align]');
        alignButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const align = btn.dataset.align;
                this.updateTextAlignment(align);
                this.updateAlignmentButtons(align);
            });
        });

        // Text size controls
        const textWidth = document.getElementById('textWidth');
        if (textWidth) {
            textWidth.addEventListener('input', (e) => {
                this.updateSelectedTextStyle('width', e.target.value + 'px');
            });
        }

        const textHeight = document.getElementById('textHeight');
        if (textHeight) {
            textHeight.addEventListener('input', (e) => {
                this.updateSelectedTextStyle('height', e.target.value + 'px');
            });
        }

        // Background opacity
        const textBgOpacity = document.getElementById('textBgOpacity');
        const textBgOpacityValue = document.getElementById('textBgOpacityValue');
        if (textBgOpacity && textBgOpacityValue) {
            textBgOpacity.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                textBgOpacityValue.textContent = e.target.value + '%';
                this.updateTextBackgroundOpacity(opacity);
            });
        }
    }
    
    updateSelectedTextStyle(property, value) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }
        
        selectedElement.style[property] = value;
    }
    
    updateSelectedTextBorder(property, value) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }
        
        switch (property) {
            case 'borderWidth':
                selectedElement.style.borderWidth = value;
                selectedElement.style.borderStyle = value === '0px' ? 'none' : 'solid';
                break;
            case 'borderColor':
                selectedElement.style.borderColor = value;
                break;
            case 'borderRadius':
                selectedElement.style.borderRadius = value;
                break;
        }
    }
    
    toggleTextStyle(button, style) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }
        
        const isActive = button.classList.contains('active');
        
        switch (style) {
            case 'bold':
                selectedElement.style.fontWeight = isActive ? 'normal' : 'bold';
                break;
            case 'italic':
                selectedElement.style.fontStyle = isActive ? 'normal' : 'italic';
                break;
            case 'underline':
                selectedElement.style.textDecoration = isActive ? 'none' : 'underline';
                break;
        }
        
        button.classList.toggle('active');
    }
    
    updateColorPreview(input, color) {
        const preview = input.nextElementSibling;
        if (preview && preview.classList.contains('kms-color-preview')) {
            preview.style.backgroundColor = color;
            preview.dataset.color = color;
        }
    }

    updateTextShadow() {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }

        const shadowBlur = document.getElementById('shadowBlur');
        const shadowDistance = document.getElementById('shadowDistance');
        const shadowColor = document.getElementById('shadowColor');

        if (shadowBlur && shadowDistance && shadowColor) {
            const blur = shadowBlur.value;
            const distance = shadowDistance.value;
            const color = shadowColor.value;

            if (blur === '0' && distance === '0') {
                selectedElement.style.textShadow = 'none';
            } else {
                selectedElement.style.textShadow = `${distance}px ${distance}px ${blur}px ${color}`;
            }
        }
    }

    updateTextAlignment(align) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }

        selectedElement.style.textAlign = align;
    }

    updateAlignmentButtons(activeAlign) {
        const alignButtons = document.querySelectorAll('[data-align]');
        alignButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.align === activeAlign);
        });
    }

    updateTextBackgroundOpacity(opacity) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }

        const bgColor = document.getElementById('textBgColor').value;
        const rgb = this.hexToRgb(bgColor);
        if (rgb) {
            selectedElement.style.backgroundColor = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`;
        }
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
    
    // Update controls when a text element is selected
    updateControlsForTextElement(element) {
        const computedStyle = window.getComputedStyle(element);
        
        // Update font family
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            // Try to match the current font with available options
            const currentFont = computedStyle.fontFamily.toLowerCase();
            const options = fontFamily.options;

            for (let i = 0; i < options.length; i++) {
                const optionFont = options[i].value.toLowerCase();
                if (currentFont.includes(optionFont.split(',')[0].trim())) {
                    fontFamily.selectedIndex = i;
                    break;
                }
            }
        }
        
        // Update font size
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        if (fontSize && fontSizeValue) {
            const currentSize = parseInt(computedStyle.fontSize);
            fontSize.value = currentSize;
            fontSizeValue.textContent = currentSize + 'px';
        }
        
        // Update font color
        const fontColor = document.getElementById('fontColor');
        if (fontColor) {
            const currentColor = this.rgbToHex(computedStyle.color);
            fontColor.value = currentColor;
            this.updateColorPreview(fontColor, currentColor);
        }
        
        // Update style buttons
        this.updateStyleButtons(computedStyle);
        
        // Update border controls
        this.updateBorderControls(computedStyle);
    }
    
    updateStyleButtons(computedStyle) {
        const boldBtn = document.getElementById('boldBtn');
        const italicBtn = document.getElementById('italicBtn');
        const underlineBtn = document.getElementById('underlineBtn');
        
        if (boldBtn) {
            boldBtn.classList.toggle('active', 
                computedStyle.fontWeight === 'bold' || parseInt(computedStyle.fontWeight) >= 600);
        }
        
        if (italicBtn) {
            italicBtn.classList.toggle('active', computedStyle.fontStyle === 'italic');
        }
        
        if (underlineBtn) {
            underlineBtn.classList.toggle('active', 
                computedStyle.textDecoration.includes('underline'));
        }
    }
    
    updateBorderControls(computedStyle) {
        const borderWidth = document.getElementById('borderWidth');
        const borderRadius = document.getElementById('borderRadius');
        const borderColor = document.getElementById('borderColor');
        
        if (borderWidth) {
            const currentWidth = parseInt(computedStyle.borderWidth) || 0;
            borderWidth.value = currentWidth;
        }
        
        if (borderRadius) {
            const currentRadius = parseInt(computedStyle.borderRadius) || 0;
            borderRadius.value = currentRadius;
        }
        
        if (borderColor) {
            const currentColor = this.rgbToHex(computedStyle.borderColor);
            borderColor.value = currentColor;
            this.updateColorPreview(borderColor, currentColor);
        }
    }
    
    // Utility function to convert RGB to HEX
    rgbToHex(rgb) {
        if (rgb.startsWith('#')) return rgb;
        
        const result = rgb.match(/\d+/g);
        if (!result || result.length < 3) return '#000000';
        
        const r = parseInt(result[0]);
        const g = parseInt(result[1]);
        const b = parseInt(result[2]);
        
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }
    
    // Create a new text element with default styling
    createTextElement(text = null) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return null;
        
        const textElement = document.createElement('div');
        textElement.className = 'kms-canvas-element kms-text-element';
        textElement.contentEditable = true;
        textElement.textContent = text || (this.posterMaker.currentLanguage === 'en' ? 'Double click to edit' : '雙擊編輯文字');
        
        // Default styling
        textElement.style.left = '50px';
        textElement.style.top = '50px';
        textElement.style.fontSize = '24px';
        textElement.style.fontFamily = 'Arial, sans-serif';
        textElement.style.color = '#333333';
        textElement.style.padding = '8px';
        textElement.style.minWidth = '50px';
        textElement.style.minHeight = '30px';
        textElement.style.outline = 'none';
        textElement.style.border = 'none';
        textElement.style.backgroundColor = 'transparent';
        textElement.style.lineHeight = '1.4';
        textElement.style.wordWrap = 'break-word';
        textElement.style.whiteSpace = 'pre-wrap';
        textElement.style.textShadow = 'none';
        
        textElement.dataset.elementId = `text_${++this.posterMaker.elementCounter}`;
        
        // Setup interactions
        this.posterMaker.setupElementInteraction(textElement);
        this.setupTextEditing(textElement);

        // Add to canvas and elements array
        canvas.appendChild(textElement);
        this.posterMaker.elements.push(textElement);

        return textElement;
    }
    
    // Handle text element editing
    setupTextEditing(element) {
        // Double-click to edit
        element.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            element.focus();
            // Select all text for easy editing
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        });

        element.addEventListener('blur', () => {
            // Save changes when element loses focus
            if (element.textContent.trim() === '') {
                element.textContent = this.posterMaker.currentLanguage === 'en' ? 'Double click to edit' : '雙擊編輯文字';
            }
        });

        element.addEventListener('keydown', (e) => {
            // Prevent dragging while editing
            e.stopPropagation();

            // Handle Enter key
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                element.blur();
            }
        });

        element.addEventListener('input', () => {
            // Auto-resize element based on content
            this.autoResizeTextElement(element);
        });

        // Prevent dragging when in edit mode
        element.addEventListener('mousedown', (e) => {
            if (element === document.activeElement) {
                e.stopPropagation();
            }
        });
    }
    
    autoResizeTextElement(element) {
        // Create a temporary element to measure text size
        const temp = document.createElement('div');
        temp.style.position = 'absolute';
        temp.style.visibility = 'hidden';
        temp.style.whiteSpace = 'pre-wrap';
        temp.style.wordWrap = 'break-word';
        temp.style.font = window.getComputedStyle(element).font;
        temp.style.padding = window.getComputedStyle(element).padding;
        temp.textContent = element.textContent;
        
        document.body.appendChild(temp);
        
        const rect = temp.getBoundingClientRect();
        element.style.width = Math.max(50, rect.width) + 'px';
        element.style.height = Math.max(30, rect.height) + 'px';
        
        document.body.removeChild(temp);
    }
}

// Initialize text handler when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.kmsPosterMaker) {
        window.kmsPosterMaker.textHandler = new KMSTextHandler(window.kmsPosterMaker);
    }
});
